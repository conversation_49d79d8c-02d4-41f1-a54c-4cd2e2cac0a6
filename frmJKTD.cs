using AIServices.Models.Chat;
using AIServices.Utils;
using DevExpress.Pdf;
using DevExpress.Pdf.Drawing.Extensions;
using DevExpress.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;

using System.Windows.Forms;

namespace HIH.CRM.Import.AIOCR
{
    public partial class frmJKTD : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        private readonly TaskSequenceHelper task;
        private string currentPdfPath; // 当前PDF显示的数据路径
        private string dlxx; // 代理信息
        private string fileId; // 上传文件ID
        private DataTable dt; // 列表数据

        // 定义列表默认数据
        private readonly string[] fieldNames = { "分单号", "总单号", "船名航次", "起运港", "目的港", "件数", "体积", "毛重", "离港日期" };


        private bool showHighlight = false;
        private Rectangle viewerRect;    // 控件坐标（单位是像素
        private Timer highlightTimer;
        private RectangleF? documentRect; // 文档坐标（单位是 point）
        private int? documentPageNumber;


        public frmJKTD()
        {
            InitializeComponent();
            isLoadPerm = false;
            task = new TaskSequenceHelper("414242d05ce04f8f87b811dc7f42aa1c");

            // 初始化 Timer
            highlightTimer = new Timer();
            highlightTimer.Interval = 10000; // 显示1秒
            highlightTimer.Tick += HighlightTimer_Tick;

            // 绑定 PdfViewer 的 Paint 事件
            pdfView.Paint += PdfViewer1_Paint;

            InitializeGridData(); // 初始化GridView数据
        }

        private void InitializeGridData()
        {
            dt = new DataTable();
            dt.Columns.Add("key", typeof(string));
            dt.Columns.Add("value", typeof(string));

            // 填写列表数据
            foreach (string fieldName in fieldNames)
            {
                DataRow row = dt.NewRow();
                row["key"] = fieldName;
                row["value"] = ""; // 初始值为空
                dt.Rows.Add(row);
            }
            //绑定
            gd.DataSource = dt;
            gdv.BestFitColumns();
        }

        private void btnSelectFile_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF文件|*.pdf",
                Title = "选择PDF文件"
            };
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                fileId = "";
                //在PDFview打开
                currentPdfPath = openFileDialog.FileName;
                pdfView.LoadDocument(openFileDialog.FileName);
                foreach (DataRow row in dt.Rows)
                {
                    row["value"] = ""; // 重置列表数据
                }
            }

        }

        private async void btnStartOCR_ItemClickAsync(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {


            if (string.IsNullOrEmpty(currentPdfPath))
            {
                ICF.ISD.ShowError("请先选择PDF文件！");
                return;
            }
            try
            {
                using (WaitDialogForm waitDialog = new WaitDialogForm("正在提取准备中...",
                                                    "请稍候",
                                                    new Size(500, 100), this))
                {


                    waitDialog.SetCaption("正在解析文件...");


                    foreach (DataRow row in dt.Rows)
                    {
                        row["value"] = ""; // 重置为默认空值
                    }
                    if (string.IsNullOrEmpty(fileId))
                    {
                        using (FileStream fileStream = File.OpenRead(currentPdfPath))
                        {
                            string fileName = Path.GetFileName(currentPdfPath);
                            fileId = await task.UploadFileAsync(fileStream, fileName);
                            //已经获取到fileID,将获取到的fileId，查询状态
                            string status = await task.PollFileStatusWithTimeoutAsync(fileId, 180);
                            if (status != "FILE_IS_READY")
                            {
                                ICF.ISD.ShowError("文件解析失败，状态：" + status + "！");
                                return;
                            }
                        }
                    }
                    waitDialog.SetCaption("解析文件完成，正在获取提取内容...");
                    //请求AI接口
                    //string message = "{\"version\":\"1.1\",\"description\":\"不同的提单，提单组成信息位置也不一样,请了解当前材料的提单,结构化文档解析规则,不要虚拟伪造数据返回\",\"fields\":{\"分单号\":{\"description\":\"分单号，可能带FRA前缀，不会是HHJK前缀\",\"extraction_steps\":[{\"step\":1,\"action\":\"优先匹配关键词\",\"target\":[\"SEA WAYBILL No\",\"HAWB No\",\"B/L NO\"],\"cleanup\":{\"remove\":[\" \",\"-\"],\"keep_prefix\":\"FRA\"}},{\"step\":2,\"action\":\"匹配FRA及后续内容\",\"pattern\":\"FRA[\\w\\-\\/\\s]+\",\"on_match\":\"full_value\"},{\"step\":3,\"action\":\"匹配无FRA的普通分单号\",\"pattern\":\"[A-Z0-9\\-]{5,}\",\"on_match\":\"full_value\"}],\"output_rules\":{\"start\":\"如果未找到分单号信息，请理解文档来选择正确的单号提取。\",\"empty_value\":\"\"}},\"总单号\":{\"description\":\"不可能是HHJK前缀，有一定概率是和分单号同行，也能填写联系电话或者传真号码。\",\"source_priority\":[\"MAWB\",\"MAWB#\",\"MASTER B/L\",\"MBL\",\"MASTER\"],\"extraction_logic\":{\"positional_rules\":[{\"condition\":\"分单号_exists_and_starts_with_FRA\",\"extract_right_of\":\"分单号\"}]},\"processing\":{\"remove_chars\":[\"-\",\" \"],\"to_uppercase\":true},\"empty_value\":\"\"},\"船名航次\":{\"description\": \"如果有\"DHL\",本身就可以作为承运人名称（Carrier Name）使用\",\"source_priority\":[\"VESSEL AND VOYAGE NO\",\"Requested Flight\",\"DHL\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"remove_suffixes\":[\"日期\",\"ETA\",\"ETD\",\"\\d{4}-\\d{2}-\\d{2}\"]},\"output_rules\":{\"start\":\"提取船名或者航次,不需要带航空日期后缀,保留空格部分\",\"empty_value\":\"\"}},\"起运港\":{\"source_priority\":[\"PLACE AND DATE OF ISSUE\",\"Airport of Departure\",\"ALBERT-SCHWEITZER-STRASSE\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"目的港\":{\"source_priority\":[\"PORT OF DISCHARGE\",\"TO\",\"DESTINATION CODE\",\"Airport of Destination\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\",\"TO\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"件数\":{\"source_priority\":[\"Total Items\",\"Pallet(s)\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":0},\"体积\":{\"source_priority\":[\"体积字段\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"毛重\":{\"source_priority\":[\"Gross Cargo Weight\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"离港日期\":{\"description\": \"离港日期、也叫 Arrive Date\",\"source_priority\":[\"SHIPPED ON BOARD DATE\",\"Arrive Date\"],\"processing\":{\"regex\":\"[\\d\\-/\\.]+\",\"date_formats\":[\"dd/mm/yyyy\",\"mm-dd-yyyy\",\"yyyy-mm-dd\",\"yyyy/MM/dd\"],\"output_format\":\"yyyy-MM-dd\"},\"empty_value\":\"\"}},\"global_rules\":{\"numeric_default\":0,\"string_default\":\"\",\"date_default\":\"\",\"strict_mode\":false}}";

                    string message = "{\"version\":\"1.1\",\"description\":\"不同的提单，提单组成信息位置也不一样,请了解当前材料的提单,结构化文档解析规则,不要虚拟伪造数据返回\",\"fields\":{\"代理名称\": {\"description\": \"这是提单的代理名称\",\"source_priority\": [\"DHL\",\"ROHLIG\",\"WAYBILL\",\"MEDITERRANEAN\",\"FIGHT\",\"KELSTERBACH\",\"FairCon\",\"FMS\",\"YANG MING\",\"SONICA\",\"HMM\",\"MAERSK\",\"NVOCC\",\"COPYNON\",\"NEW GLOBE\",\"江苏远洋太海\",\"CN LOGISTICS\",\"上海裕辉\",\"DOUBLE NINTH\",\"Consignees Account\"],\"empty_value\": \"\"},\"分单号\":{\"description\":\"分单号，可能带FRA前缀，不会是HHJK前缀\",\"extraction_steps\":[{\"step\":1,\"action\":\"优先匹配关键词\",\"target\":[\"SEA WAYBILL No\",\"HAWB No\",\"B/L NO\"],\"cleanup\":{\"remove\":[\" \",\"-\"],\"keep_prefix\":\"FRA\"}},{\"step\":2,\"action\":\"匹配FRA及后续内容\",\"pattern\":\"FRA[\\w\\-\\/\\s]+\",\"on_match\":\"full_value\"},{\"step\":3,\"action\":\"匹配无FRA的普通分单号\",\"pattern\":\"[A-Z0-9\\-]{5,}\",\"on_match\":\"full_value\"}],\"output_rules\":{\"start\":\"如果未找到分单号信息，请理解文档来选择正确的单号提取。\",\"empty_value\":\"\"}},\"总单号\":{\"description\":\"不可能是HHJK前缀，有一定概率是和分单号同行，也能填写联系电话或者传真号码。\",\"source_priority\":[\"MAWB\",\"MAWB#\",\"MASTER B/L\",\"MBL\",\"MASTER\"],\"extraction_logic\":{\"positional_rules\":[{\"condition\":\"分单号_exists_and_starts_with_FRA\",\"extract_right_of\":\"分单号\"}]},\"processing\":{\"remove_chars\":[\"-\",\" \"],\"to_uppercase\":true},\"empty_value\":\"\"},\"船名航次\":{\"description\": \"如果有\"DHL\",本身就可以作为承运人名称（Carrier Name）使用\",\"source_priority\":[\"VESSEL AND VOYAGE NO\",\"Requested Flight\",\"DHL\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"remove_suffixes\":[\"日期\",\"ETA\",\"ETD\",\"\\d{4}-\\d{2}-\\d{2}\"]},\"output_rules\":{\"start\":\"提取船名或者航次,不需要带航空日期后缀,保留空格部分\",\"empty_value\":\"\"}},\"起运港\":{\"source_priority\":[\"PLACE AND DATE OF ISSUE\",\"Airport of Departure\",\"ALBERT-SCHWEITZER-STRASSE\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"目的港\":{\"source_priority\":[\"PORT OF DISCHARGE\",\"TO\",\"DESTINATION CODE\",\"Airport of Destination\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\",\"TO\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"件数\":{\"source_priority\":[\"Total Items\",\"Pallet(s)\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":0},\"体积\":{\"source_priority\":[\"体积字段\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"毛重\":{\"source_priority\":[\"Gross Cargo Weight\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"离港日期\":{\"description\": \"离港日期、也叫 Arrive Date\",\"source_priority\":[\"SHIPPED ON BOARD DATE\",\"Arrive Date\"],\"processing\":{\"regex\":\"[\\d\\-/\\.]+\",\"date_formats\":[\"dd/mm/yyyy\",\"mm-dd-yyyy\",\"yyyy-mm-dd\",\"yyyy/MM/dd\"],\"output_format\":\"yyyy-MM-dd\"},\"empty_value\":\"\"}},\"global_rules\":{\"numeric_default\":0,\"string_default\":\"\",\"date_default\":\"\",\"strict_mode\":false}}";

                    ChatRequest chatRequest = new ChatRequest();
                    chatRequest.Question = message;
                    chatRequest.FileId = new string[] { fileId };
                    chatRequest.Temperature = 0.25;
                    //请求接口
                    string result = await task.ProcessSingleRequestsAsync(chatRequest);
                    BindDynamicToGridView(result);
                }
                ICF.ISD.ShowTips("提取完成，请检查列表数据，是否正确！");
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }


        /// <summary>
        /// 针对AI返回的数据进行处理
        /// </summary>
        /// <param name="strJson"></param>
        private void BindDynamicToGridView(string strJson)
        {
            try
            {
                strJson = strJson.Trim().TrimStart('{').TrimEnd('}');
                var fieldPairs = strJson.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                // 构建字典 key 、 value
                var dict = new Dictionary<string, string>();
                foreach (var pair in fieldPairs)
                {
                    var parts = pair.Split(new[] { ':' }, 2);
                    if (parts.Length == 2)
                    {
                        string key = parts[0].Trim().Trim('"');
                        string value = parts[1].Trim().Trim('"');

                        // 处理值中可能的转义字符
                        value = value.Replace("\\\"", "\"");

                        dict[key] = value;
                    }
                }

                // 更新value 列
                foreach (DataRow row in dt.Rows)
                {
                    string field = row["key"].ToString();
                    row["value"] = dict.ContainsKey(field) ? dict[field] : "";
                }
                dlxx = dict.ContainsKey("代理名称") ? dict["代理名称"] : "";
                //获取到代理名称
                //根据代理名称
                txtBox.Text = dlxx;
                // 绑定到 GridView
                gdv.RefreshData();
                gdv.BestFitColumns();
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError($"绑定数据到GridView时发生错误: {ex.Message}");
            }
        }

        private void gdv_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gdv.CalcHitInfo(new Point(e.X, e.Y));
                if (e.Button == MouseButtons.Left && e.Clicks == 2)//判断是否左键双击
                {
                    //判断光标是否在行范围内
                    if (hInfo.InRow)
                    {
                        DataRow row = gdv.GetDataRow(hInfo.RowHandle);
                        string fieldName = row["key"].ToString();

                        // 使用统一的坐标配置方法获取字段坐标
                        var coordinates = GetFieldCoordinates(dlxx, fieldName);
                        if (!coordinates.HasValue)
                        {
                            // 如果没有找到对应的坐标配置，则不显示高亮
                            return;
                        }

                        var (x, y, width, height, pageNumber) = coordinates.Value;


                       


                        RectangleF pdfRect = new RectangleF(x, y, width, height);
                        viewerRect = ConvertPdfRectToViewerRect(pageNumber, pdfRect);

                        showHighlight = true;
                        pdfView.Invalidate(); // 刷新绘图
                        highlightTimer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError(ex.Message);
            }
        }



        #region 坐标配置管理

        /// <summary>
        /// 获取指定代理和字段的PDF坐标配置
        /// 坐标系统说明：
        /// 1. 不同代理可能使用不同的坐标系统（正数或负数Y坐标）
        /// 2. 负数Y坐标通常表示页面旋转或特殊的坐标变换
        /// 3. 坐标值基于实际测试结果，确保准确定位
        /// 4. 统一管理坐标配置，便于维护和调试
        /// </summary>
        /// <param name="agentName">代理名称</param>
        /// <param name="fieldName">字段名称</param>
        /// <returns>坐标配置信息(x, y, width, height, pageNumber)，如果未找到返回null</returns>
        private (float x, float y, float width, float height, int pageNumber)? GetFieldCoordinates(string agentName, string fieldName)
        {
            // 上海裕辉代理的坐标配置
            if (agentName.Contains("上海裕辉"))
            {
                switch (fieldName)
                {
                    case "分单号":
                        return (500, 510, 130, 50, 1);
                    case "总单号":
                        return (350, 510, 120, 50, 1);
                    case "船名航次":
                        return (300, 450, 150, 35, 2);
                    case "起运港":
                        return (255, 465, 250, 35, 2);
                    case "目的港":
                        return (255, 430, 190, 35, 2);
                    case "件数":
                        return (650, 510, 80, 53, 1);
                    case "毛重":
                        return (650, 460, 100, 53, 1);
                    case "离港日期":
                        return (400, 50, 200, 53, 2);
                    default:
                        return null;
                }
            }

            // MEDITERRANEAN代理的坐标配置
            if (agentName.Contains("MEDITERRANEAN"))
            {
                switch (fieldName)
                {
                    case "分单号":
                        return (400, 650, 250, 30, 1);
                    case "船名航次":
                        return (220, 490, 200, 40, 1);
                    case "起运港":
                        return (450, 490, 150, 40, 1);
                    case "目的港":
                        return (450, 450, 150, 40, 1);
                    case "件数":
                        return (400, 320, 200, 80, 1);
                    case "毛重":
                        return (620, 340, 100, 53, 1);
                    case "离港日期":
                        return (480, 100, 200, 53, 1);
                    default:
                        return null;
                }
            }

            // ROHLIG代理的坐标配置
            // 坐标系统已修复：现在使用标准PDF坐标系统（左下角为原点，Y轴向上）
            // 修复了 flippedY 计算错误，现在可以使用正常的正数坐标
            if (agentName.Contains("ROHLIG"))
            {
                switch (fieldName)
                {
                    case "分单号":
                        return (435, 750, 200, 40, 1);   // 右上角：页面顶部附近
                    case "船名航次":
                        return (26, 600, 250, 45, 1);    // 中上部
                    case "起运港":
                        return (300, 600, 220, 45, 1);   // 中上部
                    case "目的港":
                        return (230, 400, 190, 40, 1);   // 中部
                    case "件数":
                        return (450, 350, 140, 80, 1);   // 中下部
                    case "毛重":
                        return (600, 300, 80, 60, 1);    // 中下部
                    case "离港日期":
                        return (230, 250, 210, 53, 1);   // 下部
                    default:
                        return null;
                }
            }

            return null;
        }

        /// <summary>
        /// 标准化坐标配置，处理负数坐标和坐标系转换
        /// </summary>
        /// <param name="agentName">代理名称</param>
        /// <param name="x">原始X坐标</param>
        /// <param name="y">原始Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="pageNumber">页码</param>
        /// <returns>标准化后的坐标</returns>
        private (float x, float y, float width, float height, int pageNumber) NormalizeCoordinates(
            string agentName, float x, float y, float width, float height, int pageNumber)
        {
            // ROHLIG代理使用特殊的坐标系统（负数Y坐标）
            if (agentName.Contains("ROHLIG"))
            {
                // 对于ROHLIG代理，负数Y坐标是正确的，不需要转换
                // 这可能是由于PDF页面旋转或特殊的坐标变换导致的
                return (x, y, width, height, pageNumber);
            }

            // 其他代理使用标准坐标系统
            // 确保坐标在合理范围内
            float normalizedX = Math.Max(0, Math.Min(x, 800)); // 限制在页面宽度内
            float normalizedY = Math.Max(0, Math.Min(y, 1200)); // 限制在页面高度内
            float normalizedWidth = Math.Max(10, Math.Min(width, 400)); // 最小10，最大400
            float normalizedHeight = Math.Max(10, Math.Min(height, 100)); // 最小10，最大100

            return (normalizedX, normalizedY, normalizedWidth, normalizedHeight, pageNumber);
        }

        /// <summary>
        /// 坐标调试工具：分析和优化坐标配置
        /// </summary>
        /// <param name="agentName">代理名称</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="currentX">当前X坐标</param>
        /// <param name="currentY">当前Y坐标</param>
        /// <returns>调试信息和建议的优化坐标</returns>
        private string AnalyzeCoordinates(string agentName, string fieldName, float currentX, float currentY)
        {
            string analysis = $"字段: {fieldName}\n";
            analysis += $"代理: {agentName}\n";
            analysis += $"当前坐标: X={currentX}, Y={currentY}\n";

            // 分析坐标特征
            if (currentY < 0)
            {
                analysis += "⚠️ 检测到负数Y坐标\n";
                analysis += "可能原因:\n";
                analysis += "1. PDF页面被旋转了180度\n";
                analysis += "2. 使用了特殊的坐标变换\n";
                analysis += "3. DevExpress版本特殊处理\n";

                // 建议优化方案
                float suggestedY = Math.Abs(currentY);
                if (suggestedY > 800) suggestedY = 800 - Math.Abs(currentY % 800);
                analysis += $"建议尝试: Y={suggestedY} (转换为正数)\n";
            }
            else if (currentY > 1000)
            {
                analysis += "⚠️ Y坐标过大，可能超出页面范围\n";
                analysis += $"建议调整为: Y={currentY % 800}\n";
            }
            else
            {
                analysis += "✅ Y坐标在正常范围内\n";
            }

            if (currentX < 0 || currentX > 800)
            {
                analysis += "⚠️ X坐标可能超出页面范围\n";
                float suggestedX = Math.Max(0, Math.Min(currentX, 800));
                analysis += $"建议调整为: X={suggestedX}\n";
            }
            else
            {
                analysis += "✅ X坐标在正常范围内\n";
            }

            return analysis;
        }

        #endregion

        #region 绘制红框
        private void HighlightTimer_Tick(object sender, EventArgs e)
        {
            showHighlight = false;
            pdfView.Invalidate(); // 清除红框
            highlightTimer.Stop();
        }

        private void PdfViewer1_Paint(object sender, PaintEventArgs e)
        {
            if (showHighlight && documentRect.HasValue && documentPageNumber.HasValue)
            {
                try
                {
                    // 重新计算当前的显示矩形，考虑滚动和缩放变化
                    Rectangle currentRect = CalculateCurrentDisplayRect(documentPageNumber.Value, documentRect.Value);

                    if (!currentRect.IsEmpty)
                    {
                        using (Pen redPen = new Pen(Color.Red, 3))
                        {
                            e.Graphics.DrawRectangle(redPen, currentRect);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 忽略绘制异常
                }
            }
        }

        private Rectangle ConvertPdfRectToViewerRect(int pageNumber, RectangleF pdfRect)
        {
            try
            {
                // 保存文档坐标信息，用于后续绘制
                documentRect = pdfRect;
                documentPageNumber = pageNumber;

                // 先滚动到目标位置
                ScrollToCenter(pageNumber, pdfRect);

                // 计算当前显示矩形
                return CalculateCurrentDisplayRect(pageNumber, pdfRect);
            }
            catch (Exception ex)
            {
                return Rectangle.Empty;
            }
        }

        /// <summary>
        /// 计算PDF矩形在当前视图中的显示位置（考虑滚动和缩放）
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pdfRect">PDF坐标矩形</param>
        /// <returns>控件坐标矩形</returns>
        private Rectangle CalculateCurrentDisplayRect(int pageNumber, RectangleF pdfRect)
        {
            try
            {
                // 1. 确保页面有效
                if (pageNumber < 0 || pageNumber > pdfView.PageCount)
                {
                    return Rectangle.Empty;
                }

                // 2. 获取当前页大小（单位 point）
                var pdfPageSize = pdfView.GetPageSize(pageNumber);

                // 3. 计算 Y 坐标翻转（PDF左下为原点，控件左上为原点）
                // 修复：正确的转换公式应该是 pdfPageSize.Height - pdfRect.Y
                // 之前多减了 pdfRect.Height 导致坐标系统混乱
                float flippedY = pdfPageSize.Height - pdfRect.Y;

                // 4. 创建PDF坐标点（矩形左上角）
                var pdfPoint = new PdfPoint(pdfRect.X, flippedY);

                // 5. 构造定位对象
                var position = new PdfDocumentPosition(pageNumber, pdfPoint);

                // 6. 转换成控件坐标（像素）
                PointF clientPt = pdfView.GetClientPoint(position);

                // 7. 缩放比例（pdfView.ZoomFactor是百分比）
                float zoom = pdfView.ZoomFactor / 100f;

                // 8. 根据缩放计算显示的矩形大小（像素）
                int width = (int)Math.Round(pdfRect.Width * zoom);
                int height = (int)Math.Round(pdfRect.Height * zoom);

                // 9. 计算显示矩形左上角（控件坐标）
                int x = (int)Math.Round(clientPt.X);
                int y = (int)Math.Round(clientPt.Y);

                return new Rectangle(x, y, width, height);
            }
            catch (Exception ex)
            {
                return Rectangle.Empty;
            }
        }

        private void ScrollToCenter(PointF clientPt)
        {
            Size viewSize = pdfView.ClientSize;

            int offsetX = (int)(clientPt.X - viewSize.Width / 2);
            int offsetY = (int)(clientPt.Y - viewSize.Height / 2);

            pdfView.ScrollHorizontal(offsetX);
            pdfView.ScrollVertical(offsetY);
        }

        /// <summary>
        /// 滚动到指定PDF矩形的中心位置
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pdfRect">PDF坐标矩形</param>
        private void ScrollToCenter(int pageNumber, RectangleF pdfRect)
        {
            try
            {
                // 1. 首先跳转到指定页面
                if (pdfView.CurrentPageNumber != pageNumber)
                {
                    // 使用正确的DevExpress API跳转页面
                    pdfView.CurrentPageNumber = pageNumber;
                    Application.DoEvents(); // 等待页面切换完成
                }

                // 2. 获取页面大小
                var pdfPageSize = pdfView.GetPageSize(pageNumber);

                // 3. 计算矩形中心点的PDF坐标
                float centerX = pdfRect.X + pdfRect.Width / 2;
                float centerY = pdfRect.Y + pdfRect.Height / 2;

                // 4. Y坐标翻转
                float flippedCenterY = pdfPageSize.Height - centerY;

                // 5. 创建中心点位置
                var centerPoint = new PdfPoint(centerX, flippedCenterY);
                var centerPosition = new PdfDocumentPosition(pageNumber, centerPoint);

                // 6. 获取中心点的客户端坐标
                PointF centerClientPt = pdfView.GetClientPoint(centerPosition);

                // 7. 滚动使中心点居中显示
                ScrollToCenter(centerClientPt);

                // 8. 强制刷新
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                // 忽略滚动异常
            }
        }

        #endregion

        private void pdfView_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                int mouseX = e.Location.X;
                int mouseY = e.Location.Y;

                // 当前页
                int pageNumber = pdfView.CurrentPageNumber;
                var pageSize = pdfView.GetPageSize(pageNumber);

                // 当前缩放比例（1.0表示100%）
                float zoom = pdfView.ZoomFactor / 100f;

                // 计算PDF坐标（与case中使用的坐标系统完全一致）
                float x = mouseX / zoom;

                // Y坐标计算：需要反向推导CalculateCurrentDisplayRect中的逻辑
                // 在CalculateCurrentDisplayRect中：
                // flippedY = pdfPageSize.Height - pdfRect.Y - pdfRect.Height
                // pdfPoint = new PdfPoint(pdfRect.X, flippedY)
                // clientPt = pdfView.GetClientPoint(position)

                // 反向计算：
                // 1. 从客户端坐标转换为PDF坐标
                var clientPoint = new Point(mouseX, mouseY);
                // 2. 使用DevExpress API反向获取PDF位置
                try
                {
                    var pdfPosition = pdfView.GetDocumentPosition(clientPoint);
                    if (pdfPosition != null)
                    {
                        // 3. 从PDF位置反推出case中使用的Y坐标
                        // 修复后的CalculateCurrentDisplayRect中：flippedY = pdfPageSize.Height - pdfRect.Y
                        // 而pdfPosition.Point.Y就是flippedY
                        // 所以：pdfRect.Y = pdfPageSize.Height - pdfPosition.Point.Y
                        double y = pageSize.Height - pdfPosition.Point.Y;

                        // 如果还有偏差，可以尝试以下调整：
                        // y = pageSize.Height - pdfPosition.Point.Y + 某个偏移量;

                        txtBox.Text = $"x = {pdfPosition.Point.X:F1}F;  y = {y:F0};  pageNumber = {pageNumber};";
                        return;
                    }
                }
                catch
                {
                    // 如果API调用失败，使用备用计算方法
                }

                // 备用计算方法（如果上面的API调用失败）
                float backupX = mouseX / zoom;
                float backupY = (mouseY / zoom) - pageSize.Height;
                txtBox.Text = $"x = {backupX:F1}F;  y = {backupY:F0};  pageNumber = {pageNumber};";
            }
            catch
            {
                // 忽略无效位置
            }
        }

        private void pdfView_MouseClick(object sender, MouseEventArgs e)
        {
            // 鼠标点击事件处理（暂时未使用）
        }

        private void pdfView_ZoomChanged(object sender, DevExpress.XtraPdfViewer.PdfZoomChangedEventArgs e)
        {
            // 缩放变化时，如果正在显示高亮，需要重新计算位置
            if (showHighlight && documentRect.HasValue && documentPageNumber.HasValue)
            {
                // 重新计算显示矩形
                viewerRect = CalculateCurrentDisplayRect(documentPageNumber.Value, documentRect.Value);
            }

            pdfView.Invalidate(); // 强制重绘
        }
    }
}
